using System.Collections.Generic;
using UnityEngine;
using XCharts.Runtime;
using Sirenix.OdinInspector;

/// <summary>
/// Controller class for managing XCharts LineChart with direct data setting capabilities
/// Can use LineChartData ScriptableObject or set data directly via code
/// </summary>
[RequireComponent(typeof(LineChart))]
public class LineChartController : MonoBehaviour
{
    [BoxGroup("Data Source"), LabelWidth(150)]
    [Tooltip("ScriptableObject containing chart data configuration")]
    public LineChartData chartData;
    
    [BoxGroup("Data Source"), LabelWidth(150)]
    [Tooltip("Auto-load data from ScriptableObject on Start")]
    public bool autoLoadOnStart = true;
    
    [Box<PERSON>roup("Runtime Settings"), LabelWidth(150)]
    [Tooltip("Enable real-time data updates")]
    public bool enableRealTimeUpdates = false;
    
    [BoxGroup("Runtime Settings"), LabelWidth(150)]
    [ShowIf("enableRealTimeUpdates")]
    [Tooltip("Update interval in seconds")]
    [Range(0.1f, 10f)]
    public float updateInterval = 1f;

    [BoxGroup("Debug"), LabelWidth(150)]
    [Tooltip("Enable debug logging")]
    public bool enableDebugLogging = false;

    private LineChart lineChart;
    private float lastUpdateTime;
    private Dictionary<string, int> seriesIndexMap = new Dictionary<string, int>();

    #region Unity Lifecycle

    private void Awake()
    {
        lineChart = GetComponent<LineChart>();
        if (lineChart == null)
        {
            Debug.LogError($"[{gameObject.name}] LineChartController: LineChart component not found!");
        }
    }

    private void Start()
    {
        if (autoLoadOnStart && chartData != null)
        {
            LoadFromScriptableObject();
        }
    }

    private void Update()
    {
        if (enableRealTimeUpdates && Time.time - lastUpdateTime >= updateInterval)
        {
            RefreshChart();
            lastUpdateTime = Time.time;
        }
    }

    #endregion

    #region ScriptableObject Integration

    /// <summary>
    /// Load chart configuration and data from the assigned ScriptableObject
    /// </summary>
    [Button("Load from ScriptableObject")]
    public void LoadFromScriptableObject()
    {
        if (chartData == null)
        {
            Debug.LogWarning($"[{gameObject.name}] LineChartController: No LineChartData assigned!");
            return;
        }

        if (!chartData.IsValid())
        {
            Debug.LogWarning($"[{gameObject.name}] LineChartController: LineChartData is not valid!");
            return;
        }

        InitializeChart();
        SetupChartAppearance();
        SetupAxes();
        LoadSeriesData();

        if (enableDebugLogging)
        {
            Debug.Log($"[{gameObject.name}] LineChartController: Loaded chart data from {chartData.name}");
        }
    }

    /// <summary>
    /// Save current chart configuration to the ScriptableObject
    /// </summary>
    [Button("Save to ScriptableObject")]
    public void SaveToScriptableObject()
    {
        if (chartData == null)
        {
            Debug.LogWarning($"[{gameObject.name}] LineChartController: No LineChartData assigned to save to!");
            return;
        }

        // This would require reading current chart state and updating the ScriptableObject
        // Implementation depends on specific requirements
        Debug.Log($"[{gameObject.name}] LineChartController: Save to ScriptableObject functionality can be implemented based on needs");
    }

    #endregion

    #region Direct Data Setting

    /// <summary>
    /// Set chart data directly without using ScriptableObject
    /// </summary>
    public void SetChartData(string title, List<string> xLabels, Dictionary<string, List<double>> seriesData)
    {
        if (lineChart == null) return;

        InitializeChart();
        
        // Set title - ensure the component exists first
        var titleComponent = lineChart.EnsureChartComponent<Title>();
        if (titleComponent != null)
        {
            titleComponent.text = title;
        }
        else
        {
            Debug.LogWarning($"[{gameObject.name}] LineChartController: Could not create or access Title component");
        }

        // Clear existing data
        lineChart.RemoveData();
        seriesIndexMap.Clear();

        // Add X-axis labels
        foreach (string label in xLabels)
        {
            lineChart.AddXAxisData(label);
        }

        // Add series
        int seriesIndex = 0;
        foreach (var kvp in seriesData)
        {
            string seriesName = kvp.Key;
            List<double> dataPoints = kvp.Value;

            var serie = lineChart.AddSerie<Line>(seriesName);
            seriesIndexMap[seriesName] = seriesIndex;

            // Add data points
            foreach (double value in dataPoints)
            {
                lineChart.AddData(seriesIndex, value);
            }

            seriesIndex++;
        }

        RefreshChart();

        if (enableDebugLogging)
        {
            Debug.Log($"[{gameObject.name}] LineChartController: Set chart data directly - {seriesData.Count} series, {xLabels.Count} data points");
        }
    }

    /// <summary>
    /// Add a single data series
    /// </summary>
    public void AddSeries(string seriesName, List<double> dataPoints, Color? lineColor = null)
    {
        if (lineChart == null) return;

        var serie = lineChart.AddSerie<Line>(seriesName);
        int seriesIndex = lineChart.series.Count - 1;
        seriesIndexMap[seriesName] = seriesIndex;

        // Set line color if provided
        if (lineColor.HasValue)
        {
            serie.lineStyle.color = lineColor.Value;
        }

        // Add data points
        foreach (double value in dataPoints)
        {
            lineChart.AddData(seriesIndex, value);
        }

        RefreshChart();

        if (enableDebugLogging)
        {
            Debug.Log($"[{gameObject.name}] LineChartController: Added series '{seriesName}' with {dataPoints.Count} data points");
        }
    }

    /// <summary>
    /// Update data for an existing series
    /// </summary>
    public void UpdateSeriesData(string seriesName, List<double> newDataPoints)
    {
        if (lineChart == null || !seriesIndexMap.ContainsKey(seriesName)) return;

        int seriesIndex = seriesIndexMap[seriesName];
        var serie = lineChart.GetSerie(seriesIndex);
        
        if (serie != null)
        {
            // Clear existing data
            serie.ClearData();
            
            // Add new data points
            foreach (double value in newDataPoints)
            {
                lineChart.AddData(seriesIndex, value);
            }

            RefreshChart();

            if (enableDebugLogging)
            {
                Debug.Log($"[{gameObject.name}] LineChartController: Updated series '{seriesName}' with {newDataPoints.Count} data points");
            }
        }
    }

    /// <summary>
    /// Add a single data point to an existing series
    /// </summary>
    public void AddDataPoint(string seriesName, double value)
    {
        if (lineChart == null || !seriesIndexMap.ContainsKey(seriesName)) return;

        int seriesIndex = seriesIndexMap[seriesName];
        lineChart.AddData(seriesIndex, value);

        if (enableDebugLogging)
        {
            Debug.Log($"[{gameObject.name}] LineChartController: Added data point {value} to series '{seriesName}'");
        }
    }

    /// <summary>
    /// Set X-axis labels
    /// </summary>
    public void SetXAxisLabels(List<string> labels)
    {
        if (lineChart == null) return;

        // Clear existing X-axis data
        var xAxis = lineChart.GetChartComponent<XAxis>();
        if (xAxis != null)
        {
            xAxis.data.Clear();
        }

        // Add new labels
        foreach (string label in labels)
        {
            lineChart.AddXAxisData(label);
        }

        RefreshChart();
    }

    /// <summary>
    /// Set Y-axis range
    /// </summary>
    public void SetYAxisRange(float min, float max)
    {
        if (lineChart == null) return;

        var yAxis = lineChart.GetChartComponent<YAxis>();
        if (yAxis != null)
        {
            yAxis.minMaxType = Axis.AxisMinMaxType.Custom;
            yAxis.min = min;
            yAxis.max = max;
        }

        RefreshChart();
    }

    #endregion

    #region Chart Management

    /// <summary>
    /// Initialize the chart with basic components
    /// </summary>
    private void InitializeChart()
    {
        if (lineChart == null) return;

        lineChart.EnsureChartComponent<GridCoord>();
        lineChart.EnsureChartComponent<XAxis>();
        lineChart.EnsureChartComponent<YAxis>();
        lineChart.EnsureChartComponent<Title>();
        lineChart.EnsureChartComponent<Legend>();
    }

    /// <summary>
    /// Setup chart appearance from ScriptableObject
    /// </summary>
    private void SetupChartAppearance()
    {
        if (chartData == null) return;

        // Set title
        var title = lineChart.GetChartComponent<Title>();
        if (title != null)
        {
            title.text = chartData.chartTitle;
            title.subText = chartData.chartSubtitle;
        }

        // Set chart size
        var rectTransform = GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            rectTransform.sizeDelta = new Vector2(chartData.chartWidth, chartData.chartHeight);
        }
    }

    /// <summary>
    /// Setup axes from ScriptableObject
    /// </summary>
    private void SetupAxes()
    {
        if (chartData == null) return;

        // Setup Y-axis
        var yAxis = lineChart.GetChartComponent<YAxis>();
        if (yAxis != null && chartData.useCustomYRange)
        {
            yAxis.minMaxType = Axis.AxisMinMaxType.Custom;
            yAxis.min = chartData.yAxisMin;
            yAxis.max = chartData.yAxisMax;
        }

        // Add X-axis labels
        foreach (string label in chartData.xAxisLabels)
        {
            lineChart.AddXAxisData(label);
        }
    }

    /// <summary>
    /// Load series data from ScriptableObject
    /// </summary>
    private void LoadSeriesData()
    {
        if (chartData == null) return;

        seriesIndexMap.Clear();

        for (int i = 0; i < chartData.seriesData.Count; i++)
        {
            var seriesData = chartData.seriesData[i];
            if (!seriesData.isVisible) continue;

            var serie = lineChart.AddSerie<Line>(seriesData.seriesName);
            seriesIndexMap[seriesData.seriesName] = i;

            // Configure series appearance
            ConfigureSeries(serie, seriesData);

            // Add data points
            foreach (double value in seriesData.dataPoints)
            {
                lineChart.AddData(i, value);
            }
        }
    }

    /// <summary>
    /// Configure series appearance based on LineSeriesData
    /// </summary>
    private void ConfigureSeries(Line serie, LineChartData.LineSeriesData seriesData)
    {
        // Set line style - LineStyle is a direct property, not a component
        serie.lineStyle.color = seriesData.lineColor;
        serie.lineStyle.width = seriesData.lineWidth;

        // Set line type - convert from our custom enum to XCharts enum
        switch (seriesData.lineType)
        {
            case LineChartData.LineType.Normal:
                serie.lineType = XCharts.Runtime.LineType.Normal;
                serie.lineStyle.type = LineStyle.Type.Solid;
                break;
            case LineChartData.LineType.Smooth:
                serie.lineType = XCharts.Runtime.LineType.Smooth;
                break;
            case LineChartData.LineType.Dashed:
                serie.lineType = XCharts.Runtime.LineType.Normal;
                serie.lineStyle.type = LineStyle.Type.Dashed;
                break;
        }

        // Set symbols - SerieSymbol is a direct property (symbol), not a component
        serie.symbol.show = seriesData.showSymbols;
        serie.symbol.size = seriesData.symbolSize;

        // Set area style - AreaStyle is a component that needs to be ensured
        if (seriesData.showArea)
        {
            var areaStyle = serie.EnsureComponent<AreaStyle>();
            areaStyle.show = true;
            areaStyle.color = seriesData.areaColor;
        }
    }

    /// <summary>
    /// Refresh the chart display
    /// </summary>
    public void RefreshChart()
    {
        if (lineChart != null)
        {
            lineChart.RefreshChart();
        }
    }

    /// <summary>
    /// Clear all chart data
    /// </summary>
    [Button("Clear Chart")]
    public void ClearChart()
    {
        if (lineChart != null)
        {
            lineChart.RemoveData();
            seriesIndexMap.Clear();
            RefreshChart();
        }
    }

    #endregion
}
